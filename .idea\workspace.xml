<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="5d39ef2a-bbff-4381-9e91-b610273dfc9f" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="-9" />
    <option name="y" value="-9" />
    <option name="width" value="955" />
    <option name="height" value="1040" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="PackagesPane" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="shopping_front" type="b2602c69:ProjectViewProjectNode" />
              <item name="shopping_front" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="shopping_front" type="b2602c69:ProjectViewProjectNode" />
              <item name="shopping_front" type="462c0819:PsiDirectoryNode" />
              <item name="shoppingOnline_front-2025" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="shopping_front" type="b2602c69:ProjectViewProjectNode" />
              <item name="shopping_front" type="462c0819:PsiDirectoryNode" />
              <item name="shoppingOnline_front-2025" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../shopping_online/shoppingOnline_backend-2025" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5d39ef2a-bbff-4381-9e91-b610273dfc9f" name="Default Changelist" comment="" />
      <created>1757055615471</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757055615471</updated>
      <workItem from="1757055616672" duration="34000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="34000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-7" y="-7" width="764" height="832" extended-state="0" />
    <layout>
      <window_info id="Image Layers" />
      <window_info id="Designer" />
      <window_info id="UI Designer" />
      <window_info id="Capture Tool" />
      <window_info id="Favorites" side_tool="true" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24929178" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="Palette" />
      <window_info anchor="right" id="Theme Preview" />
      <window_info anchor="right" id="Maven" />
      <window_info anchor="right" id="Capture Analysis" />
      <window_info anchor="right" id="Palette&#9;" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
</project>