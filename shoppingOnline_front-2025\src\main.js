// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import request from '@/utils/request'
import axios from 'axios';

// 创建应用
const app = createApp(App)

// 全局混入（Vue 3）
app.mixin({
  created() { /* ... */ },
});

// 设置全局属性
app.config.globalProperties.$request = request
app.config.globalProperties.$http = axios;

// 使用 Pinia
app.use(createPinia())

// 使用 Element Plus
app.use(ElementPlus)

// 使用 Vue Router
app.use(router)

// 挂载
app.mount('#app')