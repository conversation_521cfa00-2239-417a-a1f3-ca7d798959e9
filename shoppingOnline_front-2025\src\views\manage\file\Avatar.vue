<template>
  <div>
    <!--          表格-->
    <el-table :data="tableData" background-color="black" @selection-change="handleSelectionChange" >
      <el-table-column type="selection" ></el-table-column>
      <el-table-column  label="头像" width="150" >
        <template    v-slot:default="scope">
          <img :src="baseApi + scope.row.url"  min-width="100" height="100" />
        </template>
      </el-table-column>

      <el-table-column prop="type" label="文件类型" width="180" ></el-table-column>
      <el-table-column prop="size" label="文件大小" width="180" ></el-table-column>

      <el-table-column label="操作">
        <template  v-slot:default="scope">

<!--          下载-->
          <a :href="baseApi + scope.row.url">
            <el-button
              type="success"
              style="font-size: 18px;"
              >
              下载
            </el-button>
          </a>
<!--          删除-->
          <el-button
              type="danger"
              style="margin-left: 10px;font-size: 18px;"
              @click="handleDelete(scope.row.id)">
              
              删除
            </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="flex: 0 0 auto">
      <el-pagination
          :current-page="currentPage"
          :page-sizes="[3, 5, 8, 10]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentPage"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { apiRequest } from "@/utils/request";
import { baseURL } from "@/utils/request";
import { ElMessage, ElMessageBox } from "element-plus";

export default {
  name: "Avatar",
  created() {
    this.load();
  },
  data(){
    return{
      baseApi: baseURL,
      tableData: [],
      total: 0,
      pageSize: 5,
      currentPage: 1,
      multipleSelection: []
    }
  },
  methods:{
    handleSizeChange(pageSize){
      this.pageSize = pageSize;
      this.load();
    },
    handleCurrentPage(currentPage){
      this.currentPage = currentPage;
      this.load();
    },
    handleSelectionChange(val){
      this.multipleSelection = val
    },
    async load() {
      if (this.isUnmounted) return; // ✅ 防止组件卸载后更新

      try {
        const res = await apiRequest({
          url: "/avatar/page",
          method: "get",
          params: {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            fileName: this.fileName,
          },
        });

        if (this.isUnmounted) return; // ✅ 再次检查

        if (res.code === "200") {
          this.tableData = res.data.records;

          // 格式化文件大小
          for (let s of this.tableData) {
            const size = s.size;
            if (size < 1024) {
              s.size = size + " B";
            } else if (size < 1024 * 1024) {
              s.size = (size / 1024).toFixed(2) + " KB";
            } else if (size < 1024 * 1024 * 1024) {
              s.size = (size / (1024 * 1024)).toFixed(2) + " MB";
            } else {
              s.size = (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
            }
          }

          this.total = res.data.total;
        } else {
          if (!this.isUnmounted) {
            ElMessage.error(res.msg || "加载失败");
          }
        }
      } catch (e) {
        if (!this.isUnmounted) {
          ElMessage({
            showClose: true,
            message: e.message || "请求失败",
            type: "error",
            duration: 5000,
          });
        }
        console.error(e);
      }
    },
    
    search(){
      this.currentPage = 1;
      this.load();
    },
    // //编辑
    // handleEdit(row){
    //   this.user = JSON.parse(JSON.stringify(row));
    //   this.dialogTitle='编辑用户';
    //   this.dialogFormVisible = true;
    // },
    // 单个删除
    async handleDelete(id) {
      try {
        await ElMessageBox.confirm("确认删除该文件吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const res = await apiRequest({
          url: `/avatar/${id}`,
          method: "delete",
        });
        if (res.code === "200") {
          ElMessage.success("删除成功");
          this.load();
        } else {
          ElMessage.error(res.msg || "删除失败");
        }
      } catch (e) {
        if (e !== "cancel" && !this.isUnmounted) {
          ElMessage.error("删除失败：");
        }
      }
    },
  },
}
</script>

<style scoped>

</style>