 <!--  -->
 <template>
  <div>
    <SearchView @search="handleSearch"></SearchView>
    <div class="main-box">
      <div style="margin: 20px auto">
        <h2 class="cate" style="font-size: 24px;">
           选择商品分类
        </h2>
        <!--类别菜单-->
        <el-row :gutter="20" style="font-size: 18px;">
          <el-col v-for="(item, index) in icons" :key="index" :span="6">
             <!-- <i class="iconfont" v-html="item.value"></i>  -->
             <i  class="iconfont"  v-text="getIconChar(item.value)"></i>
            <span v-for="(category, index2) in item.categories" :key="index2">
              <b>
                <a
                  href="#"
                  @click.prevent="load(category.id)"
                  :class="{
                    black: categoryId == category.id,
                    grey: categoryId != category.id,
                  }"
                >
                  <span> {{ category.name }}</span>
                </a>
              </b>
              <span v-if="index2 != item.categories.length - 1"> / </span>
            </span>
          </el-col>
        </el-row>
        <hr />
        <el-row :gutter="20">
          <el-col
            :span="6"
            v-for="good in good"
            :key="good.id"
            style="margin-bottom: 20px"
          >
            <!--商品格子-->
            <router-link :to="'GoodsDetailView/' + good.id">
              <el-card :body-style="{ padding: '0px', background: '#e3f5f4' }">
                <img
                  :src="baseApi + good.imgs"
                  style="width: 100%; height: 300px"
                />
                <div style="padding: 5px 10px">
                  <span style="font-size: 18px;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                                        ><b>{{ good.name.length > 15 ? good.name.slice(0, 13) + '...' : good.name }}</b></span
                                    ><br />
                  <span style="color: red; font-size: 15px"
                    >￥{{ good.price }}</span
                  >
                </div>
              </el-card>
            </router-link>
          </el-col>
        </el-row>
      </div>
      <!--      分页-->
      <div class="block" style="text-align: center">
        <el-pagination
          background
          :hide-on-single-page="false"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentPage"
        >
        </el-pagination>
      </div>
    </div>
  </div>




 </template>
 
 <script>
import SearchView from "../../../components/Search";
import { apiRequest } from '@/utils/request'
import { baseURL } from '@/utils/request'
import { ElMessage } from 'element-plus'
 export default {
    
   name: 'GoodsListView', // ✅ 推荐使用多词组件名
 
   // 引入的组件需要注册
   components: {
    SearchView,
   },
 
   // 数据区
   data() {
     return {
        //商品列表
        good:[],
        //
        icons:[],
        total: 0,
      pageSize: 8,
      currentPage: 1,
      //选择的分类
      categoryId: Number,
      //搜索的内容
      searchText: "",
       baseApi:baseURL,
     };
   },
 
   // 计算属性
   computed: {},
 
   // 监听数据变化
   watch: {},
 
   // 方法集合
   methods: {
    
    handleCurrentPage(currentPage) {
      this.currentPage = currentPage;
      this.load();
    },
    handleSearch(text) {
      this.searchText = text;
      this.load();
    },
     //icon
     async loadIcons() {
      try {
        const res = await apiRequest({ url: '/api/icon', method: 'get' })
        if (res.code === '200') {
          this.icons = Array.isArray(res.data) ? res.data.slice(0, 6) : []
        }
      } catch (e) {
        console.error(e)
        ElMessage({ showClose: true, message: e.message || e, type: 'error', duration: 5000 })
      }
    },
    //商品列表
    async loadGoods() {
      try {
        const res = await apiRequest({ url: '/api/good/page', method: 'get' ,params: {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            searchText: this.searchText,
            categoryId: this.categoryId,
          },})
        if (res.code === '200') {
             this.good = Array.isArray(res.data.records) ? res.data.records: []
            this.total = res.data.total;
             console.log("this.good :"+this.good );

        } else {
          ElMessage.error(res.msg || '获取商品失败')
        }
      } catch (e) {
        console.error(e)
        ElMessage({ showClose: true, message: e.message || e, type: 'error', duration: 5000 })
      }
    },
      getIconChar(htmlEntity) {
      const match = htmlEntity.match(/&#x([0-9a-fA-F]+);/)
      if (match) {
        return String.fromCharCode(parseInt(match[1], 16))
      }
      return ''
    }
   },
 
   // 生命周期 - 创建完成
   created() {
        //二者一般不同时存在
    this.searchText = this.$route.query.searchText;
    this.categoryId = this.$route.query.categoryId;
      this.loadIcons();
      this.loadGoods();

   },
 
   // 生命周期 - 挂载完成
   mounted() {
     // 
   },
 
   // 生命周期 - 更新之前
   beforeUpdate() {},
 
   // 生命周期 - 更新之后
   updated() {},
 
   // 生命周期 - 卸载前
   beforeUnmount() {
     // 替代 beforeDestroy
     // 
   },
 
   // 生命周期 - 卸载后
   unmounted() {
     // 替代 destroyed
     // 
   },
 
   // keep-alive 缓存组件激活时触发
   activated() {},
 
   // keep-alive 缓存组件失活时触发
   deactivated() {}
 }
 </script>
 
 <style scoped>
 .main-box {
  background-color: white;
  border: white 2px solid;
  border-radius: 40px;
  padding: 20px 40px;
  margin: 5px auto;
}

.black {
  color: black;
}

.grey {
  color: grey;
}
.iconfont {
  font-family: 'iconfont';
  font-size: 24px;
  color: #007bff;
}
 </style>