<!--  -->
<template>
  <div>
        <SearchView @search="handleSearch"></SearchView>
        <div class="main-box">
            <div class="block" style="margin: 10px auto">
                <!--      类别菜单-->
                <div class="good-menu">
                    <ul v-for="(item, index) in icons" :key="index">
                        <li>
                            <!-- <i class="iconfont" v-html="item.value"></i> -->
                            <i  class="iconfont"  v-text="getIconChar(item.value)"></i>
                            <!--              跳转到goodList页面，参数为类别id-->
                            <span
                                v-for="(category, index2) in item.categories"
                                :key="index2"
                            >
                                <router-link
                                    :to="{
                                        path: '/goodlist',
                                        query: { categoryId: category.id },
                                    }"
                                >
                                    <a href="/person"
                                        ><span> {{ category.name }}</span></a
                                    >
                                </router-link>
                                <span
                                    v-if="index2 != item.categories.length - 1"
                                >
                                    /
                                </span>
                            </span>
                        </li>
                    </ul>
                </div>
                <!--轮播图-->
                <div>
                    <el-carousel
                        height="370px"
                        style="border-radius: 20px; width: 1080px"
                    >
                        <el-carousel-item
                            v-for="carousel in carousels"
                            :key="carousel.id"
                        >
                            <router-link :to="'/goodview/' + carousel.goodId">
                                <img
                                    style="
                                        height: 370px;
                                        width: 1080px;
                                        object-fit: contain;
                                        background-color: black;
                                    "
                                    :src="baseApi + carousel.img"
                                />
                            </router-link>
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </div>
            <!--推荐商品-->
            <div
                id="recommend"
                style="
                    margin-top: 30px;
                    font-size: 28px;
                    font-weight: 600;
                    color: #ff5e5e;
                "
            >
                推荐商品
            </div>

            <div style="margin: 20px auto">
                <el-row >
                    <el-col
                        :span="6"
                        v-for="good in goodList"
                        :key="good.id"
                        style="margin-bottom: 20px;"
                    >
                        <router-link :to="'goodview/' + good.id">
                            <el-card
                                :body-style="{
                                    padding: '0px',
                                    background: '#e3f5f4',
                                }"
                            >
                                <img
                                    :src="baseApi + good.imgs"
                                    style="width: 100%; height: 300px"
                                />
                                <div style="padding: 5px 10px">
                                    <span style="font-size: 18px;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                                        ><b>{{ good.name.length > 15 ? good.name.slice(0, 13) + '...' : good.name }}</b></span
                                    ><br />
                                    <span style="color: red; font-size: 15px"
                                        ><b>￥{{ good.price }}</b></span
                                    >
                                </div>
                            </el-card>
                        </router-link>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>

<script>
import SearchView from "../../components/Search";
import { apiRequest } from '@/utils/request'
import { baseURL } from '@/utils/request'
import { ElMessage } from 'element-plus'
export default {
  name: 'IndexView', // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {
     SearchView,
  },

  // 数据区
  data() {
    return {
            //轮播图
            carousels: [],
            //推荐商品
            goodList: [],
            //分类icon，每个icon包含id、value、categories对象数组.category：id，name
            icons: [],
            //搜索内容
            searchText: "",
            baseApi:baseURL
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
     handleSearch(text) {
            this.searchText = text;
            this.$router.push({
                path: "/goodList",
                query: { searchText: this.searchText },
            });
        },
    async loadGoods() {
      try {
        const res = await apiRequest({ url: '/api/good', method: 'get' })
        if (res.code === '200') {
           this.goodList = Array.isArray(res.data) ? res.data: []
        } else {
          ElMessage.error(res.msg || '获取商品失败')
        }
      } catch (e) {
        console.error(e)
        ElMessage({ showClose: true, message: e.message || e, type: 'error', duration: 5000 })
      }
    },
    async loadIcons() {
      try {
        const res = await apiRequest({ url: '/api/icon', method: 'get' })
        if (res.code === '200') {
          this.icons = Array.isArray(res.data) ? res.data.slice(0, 6) : []
        }
      } catch (e) {
        console.error(e)
        ElMessage({ showClose: true, message: e.message || e, type: 'error', duration: 5000 })
      }
    },

    async loadCarousels() {
      try {
        const res = await apiRequest({ url: '/api/carousel', method: 'get' })
        if (res.code === '200') {
             this.carousels = Array.isArray(res.data) ? res.data: []
        }
      } catch (e) {
        console.error(e)
        ElMessage({ showClose: true, message: e.message || e, type: 'error', duration: 5000 })
      }
    },
    getIconChar(htmlEntity) {
      const match = htmlEntity.match(/&#x([0-9a-fA-F]+);/)
      if (match) {
        return String.fromCharCode(parseInt(match[1], 16))
      }
      return ''
    }

  },

  // 生命周期 - 创建完成
  created() {
      // 暂时注释掉API调用，先显示基本页面结构
      console.log('IndexView组件已创建')
      // this.loadGoods()
      // this.loadIcons()
      // this.loadCarousels()
  },

  // 生命周期 - 挂载完成
  mounted() {
    // 
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    // 
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    // 
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {}
}
</script>

<style scoped>
.main-box {
    background-color: white;
    border: white 2px solid;
    border-radius: 40px;
    padding: 20px 40px;
    margin: 5px auto;
}
.good-menu {
    float: left;
    height: 370px;
    margin-right: 130px;
}
.good-menu li {
    list-style: none;
    overflow: hidden;
    margin-bottom: 35px;
}
.good-menu li a,
span {
    font-size: 20px;
    color: #6c6969;
}
.good-menu a span:hover {
    color: #00b7ff;
}
.iconfont {
  font-family: 'iconfont';
  font-size: 24px;
  color: #007bff;
}
</style>