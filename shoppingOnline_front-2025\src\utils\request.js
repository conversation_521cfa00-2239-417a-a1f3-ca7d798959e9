// src/utils/request.js
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 定义 baseURL
const baseURL = 'http://localhost:9197'
// ------------ axios 实例 ------------
const request = axios.create({
  baseURL: baseURL, // 根据后端实际情况修改
  timeout: 50000
})

// ------------ request 拦截器 ------------
request.interceptors.request.use(
  (config) => {
    if (!config.headers) config.headers = {}
    config.headers['Content-Type'] = 'application/json;charset=utf-8'

    // 携带 token
    try {
      const raw = localStorage.getItem('user')
      if (raw) {
        const user = JSON.parse(raw)
        if (user?.token) {
          config.headers['token'] = user.token
        }
      }
    } catch { /* ignore */ }

    return config
  },
  (error) => Promise.reject(error)
)

// ------------ response 拦截器 ------------
request.interceptors.response.use(
  (response) => {
    let res = response.data

    if (response.config.responseType === 'blob') {
      return res
    }

    if (typeof res === 'string') {
      try {
        res = res ? JSON.parse(res) : res
      } catch { /* ignore */ }
    }

    if (res?.code === '402') {
      ElMessage.error(res?.msg || '登录已过期，请重新登录')
      router.push('/login')
      return Promise.reject(new Error(res?.msg || 'Unauthorized'))
    }

    return res
  },
  (error) => {
    const msg =
      error?.response?.data?.message ||
      error?.response?.data ||
      error?.message ||
      '请求失败'
    ElMessage({ showClose: true, message: String(msg), type: 'error', duration: 5000 })
    return Promise.reject(error)
  }
)

// ------------ 单独封装 GET / POST 方法 ------------
export function get(url, params = {}, config = {}) {
  return request({
    url,
    method: 'get',
    params,
    ...config
  })
}

export function post(url, data = {}, config = {}) {
  return request({
    url,
    method: 'post',
    data,
    ...config
  })
}

// ------------ 统一封装 request 方法 ------------
/**
 * 通用请求方法
 * @param {Object} options
 * @param {string} options.url - 请求地址
 * @param {string} [options.method='get'] - 请求方法：get/post/put/delete...  默认是get请求
 * @param {Object} [options.params] - GET 查询参数
 * @param {Object} [options.data] - POST/PUT 请求体
 * @param {Object} [options.config] - 额外 axios 配置
 */
export function apiRequest({ url, method = 'get', params = {}, data = {}, config = {} }) {
  return request({
    url,
    method: method.toLowerCase(),
    params,
    data,
    ...config
  })
}

export default request
export { baseURL }