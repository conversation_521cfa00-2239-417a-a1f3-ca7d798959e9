# shopping_front
需要把 pacakge.json 给移除 然后再执行下面的命令

# 1. 卸载错误包
npm uninstall vue-vibe

# 2. 安装正确 UI 框架（以 Element Plus 为例）
npm install element-plus

# 3. 删除 node_modules 和 package-lock.json 后重装（确保干净）
rm -rf node_modules package-lock.json
npm install

# 4. 启动
npm run dev

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
