<!-- 
 * @Description: 导航栏组件 - 修复版
 * @Author:JACK
 * @Date: 2025年9月1日18:48:20
-->
<template>
  <div class="navigation">
    <el-row>
      <!-- 左侧 Logo -->
      <el-col :span="3">
        <div class="logo">
          <router-link to="/">
            <i class="iconfont icon-r-home" style="font-size: 24px;"></i>
            在线商城
          </router-link>
        </div>
      </el-col>

      <!-- 中间菜单 -->
      <el-col :span="17">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          router
          @select="handleSelect"
        >
          <el-menu-item index="/" class="menu-item">商城首页</el-menu-item>
          <el-menu-item index="/goodList" class="menu-item">商品分类</el-menu-item>
          <el-menu-item index="/cart" class="menu-item">我的购物车</el-menu-item>
          <el-menu-item index="/orderlist" class="menu-item">我的订单</el-menu-item>
          <el-menu-item v-if="role === 'admin'" index="/manage" class="menu-item">
            后台管理
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 右侧用户下拉 -->
      <el-col :span="4">
        <el-dropdown trigger="click" @command="handleCommand" style="float: right; margin-right: 60px">
          <span class="el-dropdown-link" style="cursor: pointer">
            <img
              v-if="user.avatarUrl"
              :src="baseApi + user.avatarUrl"
              class="avatar"
              alt="头像"
            />
            {{ user.nickname || '未登录' }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>

          <!-- 使用 v-slot:dropdown 替代 slot="dropdown" -->
          <template #dropdown>
            <el-dropdown-menu style="text-align: center">
              <!-- 登录 -->
              <el-dropdown-item v-show="!loginStatus" @click="gotoLogin">
                登录
              </el-dropdown-item>

              <!-- 个人信息 -->
              <el-dropdown-item v-show="loginStatus" command="profile">
                个人信息
              </el-dropdown-item>

              <!-- 退出 -->
              <el-dropdown-item v-show="loginStatus" command="logout" divided>
                退出
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'NavMenu', // ✅ 多单词组件名

  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    loginStatus: {
      type: Boolean,
      default: false
    },
    role: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      activeIndex: this.$route.path, // ✅ 根据当前路由初始化
     // baseApi: this.$store.state.baseApi // 假设已存在
      
    };
  },

  methods: {
    // 菜单选择
    handleSelect(key) {
      this.activeIndex = key;
    },

    // 跳转登录
    gotoLogin() {
      this.$router.push({ path: '/login', query: { to: this.$route.path } });
    },

    // 处理下拉命令
    handleCommand(command) {
      if (command === 'profile') {
        this.$router.push('/person');
      } else if (command === 'logout') {
        this.logout();
      }
    },

    // 退出登录
    logout() {
      localStorage.removeItem('user');
      this.$message.success('退出成功');
      // 可选：跳转到登录页
      // this.$router.push('/login');
      // 刷新页面（保留）
      this.$router.go(0);
    }
  },

  watch: {
    // 监听路由变化，更新 activeIndex
    $route(to) {
      this.activeIndex = to.path;
    }
  }
};
</script>

<style scoped>
.navigation {
  width: 100%;
  height: 60px;
  line-height: 60px;
  background-color: white;
  overflow: hidden;
}

.logo a {
  text-decoration: none;
  color: #333;
  font-size: 20px;
  font-weight: bold;
  display: block;
  text-align: center;
}

.avatar {
  width: 45px;
  height: 45px;
  border-radius: 5px;
  vertical-align: middle;
  margin-right: 8px;
}

.menu-item {
  padding-left: 50px;
  padding-right: 50px;
}

.el-dropdown-link {
  display: flex;
  align-items: center;
}
</style>