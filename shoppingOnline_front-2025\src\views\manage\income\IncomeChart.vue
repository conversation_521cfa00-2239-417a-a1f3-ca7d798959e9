<!--  -->
<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <div
        id="chartSum"
        style="
          display: inline-block;
          margin-left: 50px;
          margin-top: 30px;
          font-weight: bold;
          font-size: 22px;
          color: #ffb02a;
          border: 1px lightgrey solid;
          border-radius: 10px;
          padding: 20px;
        "
      >
        总计：￥{{ total}}
      </div>
      <!--      柱状图-->
      <el-tab-pane label="各类收入柱状图" name="bar">
        <div
          id="bar"
          style="width: 1200px; height: 500px; margin: auto auto"
        ></div>
      </el-tab-pane>
      <!--      饼图-->
      <el-tab-pane label="各类收入饼图" name="pie">
        <div
          id="pie"
          style="width: 600px; height: 600px; margin: 10px auto"
        ></div>
      </el-tab-pane>
      <!--  本周收入折线图-->
      <el-tab-pane label="本周收入" name="line1">
        <div
          id="weekLine"
          style="width: 900px; height: 500px; margin: 10px auto"
        ></div>
      </el-tab-pane>
      <!-- 本月收入折线图-->
      <el-tab-pane label="本月收入" name="line2">
        <div
          id="monthLine"
          style="width: 1500px; height: 500px; margin: 10px auto"
        ></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { apiRequest } from "@/utils/request";
import { ElMessage } from "element-plus";
export default {
  name: "IncomeChart", // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      activeName: "bar",
      sumIncome: 0,
      categoryIncomes: [],
      categoryNames: [],
      incomes: [],
      totalAll: 0,
      totalWeek: 0,
      totalMonth: 0,
      total: 0,
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    handleClick(tab) {
      switch (tab.name) {
        case "bar":
          this.total = this.totalAll;
          break;
        case "pie":
          this.total = this.totalAll;
          break;
        case "line1":
          this.total = this.totalWeek;
          break;
        case "line2":
          this.total = this.totalMonth;
          break;
      }
    },
    // 商品类别柱状图数据
    async getGoodsChart() {
      var barChart = echarts.init(document.getElementById("bar"));
      var barOption = {
        tooltip: {
          trigger: "item",
        },
        title: {
          text: "收入统计柱状图",
          x: "center",
        },
        label: {
          show: true, //是否显示
          position: "top",
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [],
            type: "bar",
          },
        ],
      };
      try {
        const res = await apiRequest({
          url: "api/income/getChart",
          method: "get",
          params: {},
        });
        if (res.code === "200") {
          let categoryIncomes = res.data.categoryIncomes;
          let categoryNames = categoryIncomes.map((item) => {
            return item.categoryName;
          });
          let incomes = categoryIncomes.map((item) => {
            return item.categoryIncome;
          });
          barOption.xAxis.data = categoryNames;
          barOption.series[0].data = incomes;
          barChart.setOption(barOption);
          //   for (let i = 0; i < categoryNames.length; i++) {
          //     let item = { value: incomes[i], name: categoryNames[i] };
          //     pieOption.series[0].data.push(item);
          //   }
          //   pieChart.setOption(pieOption);
          //计算总和
          let sum = 0;
          incomes.forEach((item) => {
            sum += item;
          });
          this.total = sum;
          this.totalAll = sum;
        }
      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "请求失败",
          type: "error",
          duration: 5000,
        });
      }
    },
  },

  // 生命周期 - 创建完成
  created() {
    //
  },

  // 生命周期 - 挂载完成
  mounted() {
    //

    this.getGoodsChart();
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    //
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    //
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {},
};
</script>

<style scoped>
</style>