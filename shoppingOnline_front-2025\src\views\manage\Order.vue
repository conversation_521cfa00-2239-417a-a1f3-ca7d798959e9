<!--  -->
<template>
  <div>
    <div>
      <el-select
        v-model="searchMode"
        placeholder="请选择订单类型"
        style="width: 150px; margin-right: 10px"
      >
        <el-option value="已支付" label="已支付"></el-option>
        <el-option value="已发货" label="已发货"></el-option>
        <el-option value="已收货" label="已收货"></el-option>
      </el-select>
      <el-input v-model="searchText" @keyup.enter="load" style="width: 200px">
        <i class="el-input__icon iconfont icon-r-find"></i
      ></el-input>
      <el-button @click="reset" type="warning" style="margin: 10px">
        重置
      </el-button>
      <el-button @click="load" type="primary" style="margin: 10px">
        搜索
      </el-button>
    </div>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="id" label="ID" width="50" sortable>
      </el-table-column>
      <el-table-column
        prop="orderNo"
        label="订单编号"
        width="200"
      ></el-table-column>
      <el-table-column
        prop="totalPrice"
        label="总价"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="userId"
        label="下单人id"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="linkUser"
        label="联系人"
        width="150"
      ></el-table-column>
      <el-table-column prop="linkPhone" label="联系电话"></el-table-column>
      <el-table-column
        prop="linkAddress"
        label="送货地址"
        width="300"
      ></el-table-column>
      <el-table-column prop="state" label="状态" width="100">
        <template v-slot:default="scope">
          <el-tag type="success" v-if="scope.row.state === '已支付'">{{
            scope.row.state
          }}</el-tag>
          <el-tag type="primary" v-if="scope.row.state === '已发货'">{{
            scope.row.state
          }}</el-tag>
          <el-tag type="info" v-if="scope.row.state === '已收货'">{{
            scope.row.state
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="下单时间"></el-table-column>
      <el-table-column fixed="right" label="操作" width="250">
        <template v-slot:default="scope">
          <el-button type="primary" @click="showDetail(scope.row)">
            详情
          </el-button>
          <el-popconfirm @confirm="delivery(scope.row)" title="确定发货吗？">
            <template #reference>
              <el-button
                v-if="scope.row.state === '已支付'"
                type="primary"
                style="margin-left: 10px"
              >
                发货
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--    分页-->
    <div style="margin-top: 10px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[3, 5, 8, 10]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!--    详情弹窗-->
    <el-dialog v-model="dialogFormVisible">
      <el-table :data="detail" background-color="black">
        <el-table-column label="图片" width="150">
          <template v-slot:default="scope">
            <img :src="baseApi + scope.row.img" min-width="100" height="100" />
          </template>
        </el-table-column>

        <el-table-column prop="goodId" label="商品id"></el-table-column>
        <el-table-column prop="goodName" label="商品名称"></el-table-column>
        <el-table-column prop="standard" label="商品规格"></el-table-column>
        <el-table-column prop="price" label="单价"></el-table-column>
        <el-table-column prop="discount" label="折扣"></el-table-column>
        <el-table-column label="实价">
          <template v-slot:default="scope">
            {{ scope.row.price * scope.row.discount }}
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量"></el-table-column>
        <el-table-column label="总价">
          <template v-slot:default="scope">
            {{ scope.row.price * scope.row.discount * scope.row.count }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { apiRequest } from "@/utils/request";
// import { ElMessage, ElMessageBox } from "element-plus";
import { ElMessage } from "element-plus";
import { baseURL } from "@/utils/request";
export default {
  name: "Order", // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      options: [],
      searchMode: "",
      searchText: "",
      user: {},
      tableData: [],
      pageNum: 1,
      pageSize: 8,
      entity: {},
      total: 0,
      dialogFormVisible: false,
      detail: [],
      baseApi: baseURL,
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.load();
    },
    handleCurrentPage(currentPage) {
      this.currentPage = currentPage;
      this.load();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //加载订单信息
    async load() {
      try {
        const res = await apiRequest({
          url: "/api/order/page",
          method: "get",
          params: {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            orderNo: this.searchText,
            state: this.searchMode,
          },
        });
        if (res.code === "200") {
          this.tableData = res.data.records;
          this.total = res.data.total;
        } else {
          ElMessage.error(res.msg || "获取订单信息失败");
        }
      } catch (e) {
        console.error(e);
        ElMessage({
          showClose: true,
          message: e.message || e,
          type: "error",
          duration: 5000,
        });
      }
    },
    search() {
      this.currentPage = 1;
      this.load();
    },
    reload() {
      this.searchParams.id = "";
      this.searchParams.username = "";
      this.searchParams.nickname = "";
      this.load();
    },
    //订单详情页
    async showDetail(row) {
      const res = await apiRequest({
        url: "/api/order/orderNo/" + row.orderNo,
        method: "get",
      });
      if (res.code === "200") {
        this.detail = [];
        this.detail.push(res.data);
        this.dialogFormVisible = true;
      } else {
        ElMessage.error(res.msg || "获取订单信息失败");
      }
    },
    //订单发货
   async delivery(row){
      const res = await apiRequest({
        url: "/api/order/delivery/" + row.orderNo,
        method: "get",
      });
      if (res.code === "200") {
         ElMessage.success(res.msg || "发货成功");
         this.load();
      } else {
        ElMessage.error(res.msg || "获取订单信息失败");
      }
    }
  },

  // 生命周期 - 创建完成
  created() {
    //
    this.load();
  },

  // 生命周期 - 挂载完成
  mounted() {
    //
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    //
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    //
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {},
};
</script>

<style scoped>
</style>